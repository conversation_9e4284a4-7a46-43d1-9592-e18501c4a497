<template>
  <el-dialog
    :title="$T('地图选点')"
    :visible.sync="visible"
    :before-close="handleClose"
    class="map-point-dialog"
  >
    <div class="map-container">
      <div id="baiduMap" class="baidu-map"></div>
      <div class="map-controls">
        <el-button type="primary" size="small" @click="getCurrentLocation">
          {{ $T("获取当前位置") }}
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="confirmSelection"
          :disabled="!selectedPoint"
        >
          {{ $T("确认选择") }}
        </el-button>
      </div>
    </div>
    <div class="selected-info" v-if="selectedPoint">
      <p>
        <strong>{{ $T("选中位置") }}:</strong>
      </p>
      <p>{{ $T("经度") }}: {{ selectedPoint.lng }}</p>
      <p>{{ $T("纬度") }}: {{ selectedPoint.lat }}</p>
      <p>
        {{ $T("地址") }}: {{ selectedPoint.address || $T("获取地址中...") }}
      </p>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button
        type="primary"
        @click="confirmSelection"
        :disabled="!selectedPoint"
      >
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { api } from "@altair/knight";

export default {
  name: "MapPointDialog",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    defaultPoint: {
      type: Object,
      default: () => ({
        lng: 116.404,
        lat: 39.915
      })
    }
  },
  data() {
    return {
      visible: this.value,
      map: null,
      marker: null,
      selectedPoint: null,
      geolocation: null
    };
  },
  watch: {
    value(newVal) {
      this.visible = newVal;
      if (newVal) {
        this.$nextTick(() => {
          this.initBaiduMap();
        });
      }
    },
    visible(newVal) {
      this.$emit("input", newVal);
    }
  },
  methods: {
    /**
     * 初始化百度地图资源
     */
    async loadBaiduMapResources() {
      try {
        await api.loadBaiduMapResource({
          jsUrls: [
            "/static/baiduMap/TextIconOverlay.js",
            "/static/baiduMap/NewRichMarker.min.js",
            "/static/baiduMap/CurveLine.js"
          ],
          cssUrls: [],
          onlineJsUrls: [
            `http://api.map.baidu.com/api?v=2.0&ak=ulIkqnv3mXwT02LuAl7qdzI2QDTFQCVJ`
          ]
        });
        console.log("百度地图资源加载完成");
      } catch (error) {
        console.error("百度地图资源加载失败:", error);
        this.$message.error(this.$T("地图资源加载失败"));
      }
    },

    /**
     * 初始化百度地图
     */
    async initBaiduMap() {
      if (!window.BMap) {
        await this.loadBaiduMapResources();
      }

      // 等待DOM渲染完成
      await this.$nextTick();

      const mapContainer = document.getElementById("baiduMap");
      if (!mapContainer) {
        console.error("地图容器未找到");
        return;
      }

      // 创建地图实例
      this.map = new window.BMap.Map("baiduMap");

      // 设置中心点和缩放级别
      const point = new window.BMap.Point(
        this.defaultPoint.lng,
        this.defaultPoint.lat
      );
      this.map.centerAndZoom(point, 15);

      // 启用鼠标滚轮缩放
      this.map.enableScrollWheelZoom(true);

      // 添加控件
      this.map.addControl(new window.BMap.NavigationControl());
      this.map.addControl(new window.BMap.ScaleControl());

      // 添加点击事件
      this.map.addEventListener("click", this.onMapClick);

      // 如果有默认点，添加标记
      if (this.defaultPoint.lng && this.defaultPoint.lat) {
        this.addMarker(this.defaultPoint);
      }
    },

    /**
     * 地图点击事件处理
     */
    onMapClick(e) {
      const point = {
        lng: e.point.lng,
        lat: e.point.lat
      };

      this.addMarker(point);
      this.getAddressByPoint(point);
    },

    /**
     * 添加标记点
     */
    addMarker(point) {
      // 清除之前的标记
      if (this.marker) {
        this.map.removeOverlay(this.marker);
      }

      // 创建新标记
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);
      this.marker = new window.BMap.Marker(baiduPoint);
      this.map.addOverlay(this.marker);

      // 设置选中点
      this.selectedPoint = { ...point };

      // 居中显示
      this.map.panTo(baiduPoint);
    },

    /**
     * 根据坐标获取地址
     */
    getAddressByPoint(point) {
      if (!window.BMap) return;

      const geoc = new window.BMap.Geocoder();
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);

      geoc.getLocation(baiduPoint, result => {
        if (result && this.selectedPoint) {
          this.selectedPoint.address = result.address;
        }
      });
    },

    /**
     * 获取当前位置
     */
    getCurrentLocation() {
      if (!window.BMap) return;

      if (!this.geolocation) {
        this.geolocation = new window.BMap.Geolocation();
      }

      this.geolocation.getCurrentPosition(result => {
        if (this.geolocation.getStatus() === window.BMAP_STATUS_SUCCESS) {
          const point = {
            lng: result.point.lng,
            lat: result.point.lat
          };

          this.addMarker(point);
          this.getAddressByPoint(point);
          this.$message.success(this.$T("定位成功"));
        } else {
          this.$message.error(this.$T("定位失败，请检查浏览器定位权限"));
        }
      });
    },

    /**
     * 确认选择
     */
    confirmSelection() {
      if (!this.selectedPoint) {
        this.$message.warning(this.$T("请先选择一个位置"));
        return;
      }

      this.$emit("confirm", this.selectedPoint);
      this.handleClose();
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.visible = false;
      this.selectedPoint = null;

      // 清理地图资源
      if (this.map) {
        this.map.removeEventListener("click", this.onMapClick);
        if (this.marker) {
          this.map.removeOverlay(this.marker);
        }
        this.map = null;
        this.marker = null;
      }
    }
  },

  mounted() {
    // 预加载百度地图资源
    this.loadBaiduMapResources();
  },

  beforeDestroy() {
    this.handleClose();
  }
};
</script>

<style lang="scss" scoped>
.map-point-dialog {
  .map-container {
    height: 500px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    .baidu-map {
      width: 100%;
      height: 100%;
    }

    .map-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;

      .el-button {
        margin-left: 8px;
      }
    }
  }

  .selected-info {
    margin-top: 16px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: #303133;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

// 全局样式，确保百度地图正常显示
:global(.map-point-dialog .el-dialog__body) {
  padding: 20px;
}
</style>
