<template>
  <div></div>
</template>
<script>
import { api } from "@altair/knight";
export default {
  name: "MapPointDialog",
  methods: {
    load() {
      api
        .loadBaiduMapResource({
          jsUrls: [
            "/static/baiduMap/TextIconOverlay.js",
            // "/static/baiduMap/MarkerClusterer.js",
            // "/static/baiduMap/NewInfoBox.js",
            "/static/baiduMap/NewRichMarker.min.js",
            "/static/baiduMap/CurveLine.js"
          ],
          cssUrls: ["http://api.map.baidu.com/res/webgl/10/bmap.css"],
          onlineJsUrls: [
            `http://api.map.baidu.com/getscript?v=3.0&ak=ulIkqnv3mXwT02LuAl7qdzI2QDTFQCVJ&services=&t=20240515114120`
          ]
        })
        .then(res => {
          console.log(res, 2222);
        });
    }
  },
  onMounted() {
    this.load();
  }
};
</script>
<style lang="scss" scoped></style>
