// 百度地图 BMap 2.0 类型声明文件
declare global {
  interface Window {
    BMap: typeof BMap;
    BMAP_STATUS_SUCCESS: number;
    BMAP_STATUS_CITY_LIST: number;
    BMAP_STATUS_UNKNOWN_LOCATION: number;
    BMAP_STATUS_UNKNOWN_ROUTE: number;
    BMAP_STATUS_INVALID_KEY: number;
    BMAP_STATUS_INVALID_REQUEST: number;
    BMAP_STATUS_PERMISSION_DENIED: number;
    BMAP_STATUS_SERVICE_UNAVAILABLE: number;
    BMAP_STATUS_TIMEOUT: number;
  }

  namespace BMap {
    class Map {
      constructor(container: string | HTMLElement, opts?: MapOptions);
      centerAndZoom(center: Point, zoom: number): void;
      enableScrollWheelZoom(enabled?: boolean): void;
      addControl(control: Control): void;
      removeControl(control: Control): void;
      addOverlay(overlay: Overlay): void;
      removeOverlay(overlay: Overlay): void;
      addEventListener(event: string, handler: Function): void;
      removeEventListener(event: string, handler: Function): void;
      panTo(center: Point): void;
      setCenter(center: Point): void;
      getCenter(): Point;
      setZoom(zoom: number): void;
      getZoom(): number;
    }

    class Point {
      constructor(lng: number, lat: number);
      lng: number;
      lat: number;
    }

    class Marker extends Overlay {
      constructor(point: Point, opts?: MarkerOptions);
      setPosition(position: Point): void;
      getPosition(): Point;
    }

    class Overlay {
      initialize(map: Map): HTMLElement;
      draw(): void;
    }

    class Control {
      constructor();
    }

    class NavigationControl extends Control {
      constructor(opts?: NavigationControlOptions);
    }

    class ScaleControl extends Control {
      constructor(opts?: ScaleControlOptions);
    }

    class Geocoder {
      constructor();
      getLocation(point: Point, callback: (result: GeocoderResult) => void, opts?: GeocoderOptions): void;
      getPoint(address: string, callback: (point: Point) => void, city?: string): void;
    }

    class Geolocation {
      constructor();
      getCurrentPosition(callback: (result: GeolocationResult) => void, opts?: PositionOptions): void;
      getStatus(): number;
    }

    interface MapOptions {
      minZoom?: number;
      maxZoom?: number;
      mapType?: MapType;
      enableHighResolution?: boolean;
      enableAutoResize?: boolean;
      enableMapClick?: boolean;
    }

    interface MarkerOptions {
      offset?: Size;
      icon?: Icon;
      enableMassClear?: boolean;
      enableDragging?: boolean;
      enableClicking?: boolean;
      raiseOnDrag?: boolean;
      draggingCursor?: string;
      rotation?: number;
      shadow?: Icon;
      title?: string;
    }

    interface NavigationControlOptions {
      anchor?: ControlAnchor;
      offset?: Size;
      type?: NavigationControlType;
      showZoomInfo?: boolean;
      enableGeolocation?: boolean;
    }

    interface ScaleControlOptions {
      anchor?: ControlAnchor;
      offset?: Size;
    }

    interface GeocoderOptions {
      extensions_town?: boolean;
      extensions_adcode?: boolean;
    }

    interface PositionOptions {
      enableHighAccuracy?: boolean;
      timeout?: number;
      maximumAge?: number;
    }

    interface GeocoderResult {
      address: string;
      addressComponents: AddressComponent;
      point: Point;
      surroundingPois: Array<any>;
      business: string;
    }

    interface AddressComponent {
      city: string;
      district: string;
      province: string;
      street: string;
      streetNumber: string;
    }

    interface GeolocationResult {
      point: Point;
      accuracy: number;
    }

    interface Size {
      width: number;
      height: number;
    }

    interface Icon {
      url: string;
      size: Size;
      anchor?: Size;
    }

    enum MapType {
      BMAP_NORMAL_MAP = 0,
      BMAP_PERSPECTIVE_MAP = 1,
      BMAP_SATELLITE_MAP = 2,
      BMAP_HYBRID_MAP = 3
    }

    enum ControlAnchor {
      BMAP_ANCHOR_TOP_LEFT = 0,
      BMAP_ANCHOR_TOP_RIGHT = 1,
      BMAP_ANCHOR_BOTTOM_LEFT = 2,
      BMAP_ANCHOR_BOTTOM_RIGHT = 3
    }

    enum NavigationControlType {
      BMAP_NAVIGATION_CONTROL_LARGE = 0,
      BMAP_NAVIGATION_CONTROL_SMALL = 1,
      BMAP_NAVIGATION_CONTROL_PAN = 2,
      BMAP_NAVIGATION_CONTROL_ZOOM = 3
    }
  }
}

export {};
